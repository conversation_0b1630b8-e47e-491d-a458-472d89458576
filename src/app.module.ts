import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { AdminModule } from './admin/admin.module.js';
import { AuthModule } from './auth/auth.module.js';
import { ClientModule } from './client/client.module.js';
import { CommonModule } from './common/common.module.js';
import { ServicesModule } from './common/services/services.module.js';
import { DatabaseModule } from './database/database.module.js';
import { DeveloperModule } from './developer/developer.module.js';
import { UploadsModule } from './uploads/uploads.module.js';
import { UsersModule } from './users/users.module.js';
import { WebhooksModule } from './webhooks/webhooks.module.js';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    DatabaseModule,
    ServicesModule, // Global services including Redis
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 10,
    }]),
    AuthModule,
    UsersModule,
    AdminModule,
    ClientModule,
    DeveloperModule,
    UploadsModule,
    WebhooksModule,
    CommonModule,
  ],
})
export class AppModule { }
