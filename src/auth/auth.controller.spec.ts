import { BadRequestException, ConflictException, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller.js';
import { AuthService } from './auth.service.js';
import { AuthResponse } from './dto/auth-response.dto.js';
import { LoginRequest } from './dto/login-request.dto.js';
import { RegisterRequest } from './dto/register-request.dto.js';
import { TokenResponse } from './dto/token-response.dto.js';

// Mock AuthService
const mockAuthService = {
  registerUser: jest.fn(),
  loginUser: jest.fn(),
  logoutUser: jest.fn(),
  refreshToken: jest.fn(),
  forgotPassword: jest.fn(),
  resetPassword: jest.fn(),
};

describe('AuthController', () => {
  let controller: AuthController;

  const mockUser = {
    id: 'user-1-id',
    email: '<EMAIL>',
    name: 'Test User',
    roles: ['user'],
    emailVerified: false,
  };

  const mockTokenResponse: TokenResponse = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    expiresIn: 900,
    tokenType: 'Bearer',
  };

  const mockAuthResponse: AuthResponse = {
    user: mockUser,
    ...mockTokenResponse,
  };

  const mockRequest = {
    ip: '127.0.0.1',
    headers: {
      'user-agent': 'test-agent',
    },
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('registerUser', () => {
    const registerData: RegisterRequest = {
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      name: 'Test User',
    };

    it('should register user successfully', async () => {
      mockAuthService.registerUser.mockResolvedValueOnce(mockAuthResponse);

      const result = await controller.registerUser(registerData);

      expect(mockAuthService.registerUser).toHaveBeenCalledWith(registerData);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should handle conflict error when user already exists', async () => {
      mockAuthService.registerUser.mockRejectedValueOnce(
        new ConflictException('User with this email already exists'),
      );

      await expect(controller.registerUser(registerData)).rejects.toThrow(ConflictException);
      await expect(controller.registerUser(registerData)).rejects.toThrow(
        'User with this email already exists',
      );

      expect(mockAuthService.registerUser).toHaveBeenCalledWith(registerData);
    });

    it('should handle service errors', async () => {
      mockAuthService.registerUser.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.registerUser(registerData)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.registerUser).toHaveBeenCalledWith(registerData);
    });
  });

  describe('loginUser', () => {
    const loginData: LoginRequest = {
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      deviceInfo: { browser: 'Chrome', os: 'Windows' },
      platform: 'web',
      location: { country: 'US', city: 'New York' },
    };

    it('should login user successfully', async () => {
      mockAuthService.loginUser.mockResolvedValueOnce(mockAuthResponse);

      const result = await controller.loginUser(loginData);

      expect(mockAuthService.loginUser).toHaveBeenCalledWith(loginData);
      expect(result).toEqual(mockAuthResponse);
    });

    it('should handle invalid credentials', async () => {
      mockAuthService.loginUser.mockRejectedValueOnce(
        new UnauthorizedException('Invalid credentials'),
      );

      await expect(controller.loginUser(loginData)).rejects.toThrow(UnauthorizedException);
      await expect(controller.loginUser(loginData)).rejects.toThrow('Invalid credentials');

      expect(mockAuthService.loginUser).toHaveBeenCalledWith(loginData);
    });

    it('should handle deactivated account', async () => {
      mockAuthService.loginUser.mockRejectedValueOnce(
        new UnauthorizedException('Account is deactivated'),
      );

      await expect(controller.loginUser(loginData)).rejects.toThrow(UnauthorizedException);
      await expect(controller.loginUser(loginData)).rejects.toThrow('Account is deactivated');

      expect(mockAuthService.loginUser).toHaveBeenCalledWith(loginData);
    });

    it('should handle service errors', async () => {
      mockAuthService.loginUser.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.loginUser(loginData)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.loginUser).toHaveBeenCalledWith(loginData);
    });
  });

  describe('logoutUser', () => {
    const mockUserWithSession = {
      id: 'user-1-id',
      email: '<EMAIL>',
      roles: ['user'],
      sid: 'session-1-id',
    };

    it('should logout user successfully', async () => {
      const mockLogoutResponse = { message: 'Logged out successfully' };
      mockAuthService.logoutUser.mockResolvedValueOnce(mockLogoutResponse);

      const result = await controller.logoutUser(mockUserWithSession, mockRequest);

      expect(mockAuthService.logoutUser).toHaveBeenCalledWith(mockUserWithSession, mockRequest);
      expect(result).toEqual(mockLogoutResponse);
    });

    it('should handle missing session ID', async () => {
      const userWithoutSession = { ...mockUserWithSession, sid: undefined };
      mockAuthService.logoutUser.mockRejectedValueOnce(
        new BadRequestException('No session ID found in token'),
      );

      await expect(controller.logoutUser(userWithoutSession, mockRequest)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.logoutUser(userWithoutSession, mockRequest)).rejects.toThrow(
        'No session ID found in token',
      );

      expect(mockAuthService.logoutUser).toHaveBeenCalledWith(userWithoutSession, mockRequest);
    });

    it('should handle service errors', async () => {
      mockAuthService.logoutUser.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.logoutUser(mockUserWithSession, mockRequest)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.logoutUser).toHaveBeenCalledWith(mockUserWithSession, mockRequest);
    });
  });

  describe('refreshToken', () => {
    const refreshTokenData = {
      refreshToken: 'mock-refresh-token',
    };

    it('should refresh token successfully', async () => {
      mockAuthService.refreshToken.mockResolvedValueOnce(mockTokenResponse);

      const result = await controller.refreshToken(refreshTokenData);

      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenData);
      expect(result).toEqual(mockTokenResponse);
    });

    it('should handle invalid refresh token', async () => {
      mockAuthService.refreshToken.mockRejectedValueOnce(
        new UnauthorizedException('Invalid refresh token'),
      );

      await expect(controller.refreshToken(refreshTokenData)).rejects.toThrow(
        UnauthorizedException,
      );
      await expect(controller.refreshToken(refreshTokenData)).rejects.toThrow(
        'Invalid refresh token',
      );

      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenData);
    });

    it('should handle service errors', async () => {
      mockAuthService.refreshToken.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.refreshToken(refreshTokenData)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenData);
    });
  });

  describe('forgotPassword', () => {
    const forgotPasswordData = {
      email: '<EMAIL>',
    };

    it('should handle forgot password request successfully', async () => {
      const mockResponse = { message: 'If the email exists, a password reset link has been sent' };
      mockAuthService.forgotPassword.mockResolvedValueOnce(mockResponse);

      const result = await controller.forgotPassword(forgotPasswordData);

      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith(forgotPasswordData);
      expect(result).toEqual(mockResponse);
    });

    it('should return same message for non-existent email (security)', async () => {
      const mockResponse = { message: 'If the email exists, a password reset link has been sent' };
      mockAuthService.forgotPassword.mockResolvedValueOnce(mockResponse);

      const result = await controller.forgotPassword({ email: '<EMAIL>' });

      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(result).toEqual(mockResponse);
    });

    it('should handle service errors', async () => {
      mockAuthService.forgotPassword.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.forgotPassword(forgotPasswordData)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith(forgotPasswordData);
    });
  });

  describe('resetPassword', () => {
    const resetPasswordData = {
      token: 'valid-reset-token',
      password: 'NewSecurePassword123!',
    };

    it('should reset password successfully', async () => {
      const mockResponse = { message: 'Password reset successfully' };
      mockAuthService.resetPassword.mockResolvedValueOnce(mockResponse);

      const result = await controller.resetPassword(resetPasswordData);

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(resetPasswordData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle invalid reset token', async () => {
      mockAuthService.resetPassword.mockRejectedValueOnce(
        new BadRequestException('Invalid or expired reset token'),
      );

      await expect(controller.resetPassword(resetPasswordData)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.resetPassword(resetPasswordData)).rejects.toThrow(
        'Invalid or expired reset token',
      );

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(resetPasswordData);
    });

    it('should handle expired reset token', async () => {
      const expiredTokenData = {
        token: 'expired-reset-token',
        password: 'NewSecurePassword123!',
      };
      mockAuthService.resetPassword.mockRejectedValueOnce(
        new BadRequestException('Invalid or expired reset token'),
      );

      await expect(controller.resetPassword(expiredTokenData)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.resetPassword(expiredTokenData)).rejects.toThrow(
        'Invalid or expired reset token',
      );

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(expiredTokenData);
    });

    it('should handle service errors', async () => {
      mockAuthService.resetPassword.mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      await expect(controller.resetPassword(resetPasswordData)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(resetPasswordData);
    });
  });

  describe('testEmail', () => {
    const testEmailData = {
      to: '<EMAIL>',
      subject: 'Test Subject',
      text: 'Test email content',
      html: '<p>Test email content</p>',
    };

    it('should send test email successfully', async () => {
      const result = await controller.testEmail(testEmailData);

      expect(result).toEqual({ message: 'Test email sent' });
    });

    it('should send test email with minimal data', async () => {
      const minimalData = { to: '<EMAIL>' };

      const result = await controller.testEmail(minimalData);

      expect(result).toEqual({ message: 'Test email sent' });
    });

    it('should handle email sending errors gracefully', async () => {
      // Since the mailer service is commented out, this test simulates what would happen
      // if there was an actual error in the email sending process
      const result = await controller.testEmail(testEmailData);

      // Currently returns success since mailer is disabled
      expect(result).toEqual({ message: 'Test email sent' });
    });

    it('should validate required email field', async () => {
      const invalidData = {
        subject: 'Test Subject',
        text: 'Test email content',
      } as any;

      // This would normally be caught by validation pipes in a real scenario
      const result = await controller.testEmail(invalidData);

      // Since validation is not enforced in the controller method itself,
      // this test documents the current behavior
      expect(result).toEqual({ message: 'Test email sent' });
    });
  });
});
