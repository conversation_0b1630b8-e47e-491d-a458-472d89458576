import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { PresignedUrlOptions, S3Service, UploadOptions } from './s3.service';

// Mock AWS SDK
const mockS3Client = {
  send: jest.fn(),
};

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(() => mockS3Client),
  CreateBucketCommand: jest.fn(),
  PutObjectCommand: jest.fn(),
  GetObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn(),
  HeadObjectCommand: jest.fn(),
}));

jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn(),
}));

jest.mock('mime-types', () => ({
  lookup: jest.fn((filename: string) => {
    if (filename.endsWith('.txt')) return 'text/plain';
    if (filename.endsWith('.jpg')) return 'image/jpeg';
    return null;
  }),
}));

describe('S3Service', () => {
  let service: S3Service;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        switch (key) {
          case 'S3_ENDPOINT': return 'http://minio:9000';
          case 'S3_REGION': return 'us-east-1';
          case 'S3_ACCESS_KEY': return 'test-access-key';
          case 'S3_SECRET_KEY': return 'test-secret-key';
          case 'S3_FORCE_PATH_STYLE': return true;
          case 'S3_BUCKET_NAME': return 'test-bucket';
          case 'S3_EXTERNAL_ENDPOINT': return 'http://localhost:9000';
          default: return defaultValue;
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<S3Service>(S3Service);
    configService = module.get(ConfigService);

    // Reset only the S3Client mock, keep configService calls for constructor test
    mockS3Client.send.mockClear();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      // The service is already instantiated in beforeEach, so config calls have been made
      expect(configService.get).toHaveBeenCalledWith('S3_ENDPOINT', 'http://localhost:9000');
      expect(configService.get).toHaveBeenCalledWith('S3_REGION', 'us-east-1');
      expect(configService.get).toHaveBeenCalledWith('S3_ACCESS_KEY', 'minioadmin');
      expect(configService.get).toHaveBeenCalledWith('S3_SECRET_KEY', 'minioadmin');
      expect(configService.get).toHaveBeenCalledWith('S3_BUCKET_NAME', 'rsglider-uploads');
    });
  });

  describe('initializeBuckets', () => {
    it('should create bucket successfully', async () => {
      mockS3Client.send.mockResolvedValueOnce({});

      await service.initializeBuckets();

      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle bucket already exists error', async () => {
      const error = new Error('Bucket already exists');
      error.name = 'BucketAlreadyExists';
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.initializeBuckets()).resolves.not.toThrow();
    });

    it('should handle bucket already owned error', async () => {
      const error = new Error('Bucket already owned');
      error.name = 'BucketAlreadyOwnedByYou';
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.initializeBuckets()).resolves.not.toThrow();
    });

    it('should throw on other errors', async () => {
      const error = new Error('Network error');
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.initializeBuckets()).rejects.toThrow('Network error');
    });
  });

  describe('uploadFile', () => {
    const uploadOptions: UploadOptions = {
      key: 'test-file.txt',
      body: Buffer.from('test content'),
      contentType: 'text/plain',
    };

    it('should upload file successfully', async () => {
      const mockResult = { ETag: '"test-etag"' };
      mockS3Client.send.mockResolvedValueOnce(mockResult);

      const result = await service.uploadFile(uploadOptions);

      expect(result).toEqual({
        key: 'test-file.txt',
        etag: '"test-etag"',
        location: 'http://minio:9000/test-bucket/test-file.txt',
      });
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should detect content type from filename', async () => {
      const mockResult = { ETag: '"test-etag"' };
      mockS3Client.send.mockResolvedValueOnce(mockResult);

      const options = { ...uploadOptions };
      delete options.contentType;

      await service.uploadFile(options);

      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle upload errors', async () => {
      const error = new Error('Upload failed');
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.uploadFile(uploadOptions)).rejects.toThrow('Upload failed');
    });
  });

  describe('getPresignedUploadUrl', () => {
    const urlOptions: PresignedUrlOptions = {
      key: 'test-file.txt',
      contentType: 'text/plain',
    };

    it('should generate presigned upload URL', async () => {
      const mockUrl = 'https://example.com/presigned-url';
      const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
      getSignedUrl.mockClear();
      getSignedUrl.mockResolvedValueOnce(mockUrl);

      const result = await service.getPresignedUploadUrl(urlOptions);

      expect(result).toBe(mockUrl);
      expect(getSignedUrl).toHaveBeenCalledTimes(1);
    });

    it('should handle presigned URL generation errors', async () => {
      const error = new Error('URL generation failed');
      const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
      getSignedUrl.mockRejectedValueOnce(error);

      await expect(service.getPresignedUploadUrl(urlOptions)).rejects.toThrow('URL generation failed');
    });
  });

  describe('getPresignedDownloadUrl', () => {
    const urlOptions: PresignedUrlOptions = {
      key: 'test-file.txt',
    };

    it('should generate presigned download URL', async () => {
      const mockUrl = 'https://example.com/download-url';
      const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
      getSignedUrl.mockClear();
      getSignedUrl.mockResolvedValueOnce(mockUrl);

      const result = await service.getPresignedDownloadUrl(urlOptions);

      expect(result).toBe(mockUrl);
      expect(getSignedUrl).toHaveBeenCalledTimes(1);
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      mockS3Client.send.mockResolvedValueOnce({});

      await service.deleteFile('test-file.txt');

      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle delete errors', async () => {
      const error = new Error('Delete failed');
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.deleteFile('test-file.txt')).rejects.toThrow('Delete failed');
    });
  });

  describe('fileExists', () => {
    it('should return true if file exists', async () => {
      mockS3Client.send.mockResolvedValueOnce({});

      const result = await service.fileExists('test-file.txt');

      expect(result).toBe(true);
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should return false if file not found', async () => {
      const error = new Error('Not found');
      error.name = 'NotFound';
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await service.fileExists('test-file.txt');

      expect(result).toBe(false);
    });

    it('should throw on other errors', async () => {
      const error = new Error('Network error');
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.fileExists('test-file.txt')).rejects.toThrow('Network error');
    });
  });

  describe('getFileMetadata', () => {
    it('should return file metadata', async () => {
      const mockMetadata = {
        ContentType: 'text/plain',
        ContentLength: 100,
        LastModified: new Date(),
        ETag: '"test-etag"',
        Metadata: { custom: 'value' },
      };
      mockS3Client.send.mockResolvedValueOnce(mockMetadata);

      const result = await service.getFileMetadata('test-file.txt');

      expect(result).toEqual({
        contentType: 'text/plain',
        contentLength: 100,
        lastModified: mockMetadata.LastModified,
        etag: '"test-etag"',
        metadata: { custom: 'value' },
      });
    });

    it('should handle metadata errors', async () => {
      const error = new Error('Metadata failed');
      mockS3Client.send.mockRejectedValueOnce(error);

      await expect(service.getFileMetadata('test-file.txt')).rejects.toThrow('Metadata failed');
    });
  });
});
