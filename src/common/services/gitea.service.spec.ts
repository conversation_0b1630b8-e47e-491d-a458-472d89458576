import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { CreateRepositoryOptionsDto } from '../dto/create-repository-options.dto';
import { CreateUserOptionsDto } from '../dto/create-user-options.dto';
import { UpdateUserOptionsDto } from '../dto/update-user-options.dto';
import { WebhookOptionsDto } from '../dto/webhook-options.dto';
import { GiteaService } from './gitea.service';

// Mock axios
const mockAxiosInstance = {
  get: jest.fn(),
  post: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
};

jest.mock('axios', () => ({
  create: jest.fn(() => mockAxiosInstance),
}));

// Mock fs
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
}));

describe('GiteaService', () => {
  let service: GiteaService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        switch (key) {
          case 'GITEA_BASE_URL': return 'http://gitea:3000';
          case 'GITEA_EXTERNAL_URL': return 'http://localhost:3001';
          case 'GITEA_ADMIN_TOKEN': return 'test-admin-token';
          default: return defaultValue;
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GiteaService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GiteaService>(GiteaService);
    configService = module.get(ConfigService);

    // Reset only axios mocks, keep configService calls for constructor test
    mockAxiosInstance.get.mockClear();
    mockAxiosInstance.post.mockClear();
    mockAxiosInstance.patch.mockClear();
    mockAxiosInstance.delete.mockClear();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(configService.get).toHaveBeenCalledWith('GITEA_BASE_URL');
      expect(configService.get).toHaveBeenCalledWith('GITEA_EXTERNAL_URL');
      expect(configService.get).toHaveBeenCalledWith('GITEA_ADMIN_TOKEN');
    });

    it('should throw error if required config is missing', async () => {
      const mockConfigServiceMissing = {
        get: jest.fn((key: string) => {
          if (key === 'GITEA_BASE_URL') return null;
          if (key === 'GITEA_ADMIN_TOKEN') return null;
          return 'some-value';
        }),
      };

      await expect(async () => {
        await Test.createTestingModule({
          providers: [
            GiteaService,
            {
              provide: ConfigService,
              useValue: mockConfigServiceMissing,
            },
          ],
        }).compile();
      }).rejects.toThrow('Gitea configuration missing');
    });
  });

  describe('testConnection', () => {
    it('should return true on successful connection', async () => {
      const mockVersionResponse = { version: '1.21.0' };
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockVersionResponse });

      const result = await service.testConnection();

      expect(result).toBe(true);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/version');
    });

    it('should return false on connection failure', async () => {
      mockAxiosInstance.get.mockRejectedValueOnce(new Error('Connection failed'));

      const result = await service.testConnection();

      expect(result).toBe(false);
    });
  });

  describe('getVersion', () => {
    it('should return version information', async () => {
      const mockVersionResponse = { version: '1.21.0' };
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockVersionResponse });

      const result = await service.getVersion();

      expect(result).toEqual(mockVersionResponse);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/version');
    });

    it('should return unknown version if response is invalid', async () => {
      mockAxiosInstance.get.mockResolvedValueOnce({ data: null });

      const result = await service.getVersion();

      expect(result).toEqual({ version: 'unknown' });
    });

    it('should throw BadRequestException on error', async () => {
      mockAxiosInstance.get.mockRejectedValueOnce(new Error('Network error'));

      await expect(service.getVersion()).rejects.toThrow(BadRequestException);
    });
  });

  describe('createUser', () => {
    const createUserOptions: CreateUserOptionsDto = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      full_name: 'Test User',
    };

    it('should create user successfully', async () => {
      const mockUserResponse = { id: 1, username: 'testuser', email: '<EMAIL>' };
      mockAxiosInstance.post.mockResolvedValueOnce({ data: mockUserResponse });

      const result = await service.createUser(createUserOptions);

      expect(result).toEqual(mockUserResponse);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/admin/users', {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'Test User',
        login_name: 'testuser',
        send_notify: false,
        source_id: 0,
        must_change_password: false,
        restricted: false,
        visibility: 'public',
      });
    });

    it('should handle user creation errors', async () => {
      const error = { response: { data: { message: 'User already exists' } } };
      mockAxiosInstance.post.mockRejectedValueOnce(error);

      await expect(service.createUser(createUserOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserByUsername', () => {
    it('should return user by username', async () => {
      const mockUser = { id: 1, username: 'testuser', email: '<EMAIL>' };
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockUser });

      const result = await service.getUserByUsername('testuser');

      expect(result).toEqual(mockUser);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser');
    });

    it('should throw NotFoundException for 404 errors', async () => {
      const error = { response: { status: 404 } };
      mockAxiosInstance.get.mockRejectedValueOnce(error);

      await expect(service.getUserByUsername('nonexistent')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for other errors', async () => {
      const error = { response: { status: 500, data: { message: 'Server error' } } };
      mockAxiosInstance.get.mockRejectedValueOnce(error);

      await expect(service.getUserByUsername('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateUser', () => {
    const updateOptions: UpdateUserOptionsDto = {
      full_name: 'Updated Name',
      email: '<EMAIL>',
    };

    it('should update user successfully', async () => {
      const mockUpdatedUser = { id: 1, username: 'testuser', full_name: 'Updated Name' };
      mockAxiosInstance.patch.mockResolvedValueOnce({ data: mockUpdatedUser });

      const result = await service.updateUser('testuser', updateOptions);

      expect(result).toEqual(mockUpdatedUser);
      expect(mockAxiosInstance.patch).toHaveBeenCalledWith('/admin/users/testuser', updateOptions);
    });

    it('should handle update errors', async () => {
      const error = { response: { data: { message: 'Update failed' } } };
      mockAxiosInstance.patch.mockRejectedValueOnce(error);

      await expect(service.updateUser('testuser', updateOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValueOnce({});

      await service.deleteUser('testuser');

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/admin/users/testuser');
    });

    it('should handle delete errors', async () => {
      const error = { response: { data: { message: 'Delete failed' } } };
      mockAxiosInstance.delete.mockRejectedValueOnce(error);

      await expect(service.deleteUser('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserRepositories', () => {
    it('should return user repositories', async () => {
      const mockRepos = [{ id: 1, name: 'repo1' }, { id: 2, name: 'repo2' }];
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockRepos });

      const result = await service.getUserRepositories('testuser');

      expect(result).toEqual(mockRepos);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser/repos', {
        params: { page: 1, limit: 50 }
      });
    });

    it('should handle custom pagination', async () => {
      const mockRepos = [{ id: 1, name: 'repo1' }];
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockRepos });

      await service.getUserRepositories('testuser', 2, 10);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/users/testuser/repos', {
        params: { page: 2, limit: 10 }
      });
    });

    it('should handle repository fetch errors', async () => {
      const error = { response: { data: { message: 'Fetch failed' } } };
      mockAxiosInstance.get.mockRejectedValueOnce(error);

      await expect(service.getUserRepositories('testuser')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getRepository', () => {
    it('should return repository by owner and name', async () => {
      const mockRepo = { id: 1, name: 'testrepo', owner: { username: 'testuser' } };
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockRepo });

      const result = await service.getRepository('testuser', 'testrepo');

      expect(result).toEqual(mockRepo);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/repos/testuser/testrepo');
    });

    it('should throw NotFoundException for 404 errors', async () => {
      const error = { response: { status: 404 } };
      mockAxiosInstance.get.mockRejectedValueOnce(error);

      await expect(service.getRepository('testuser', 'nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createRepository', () => {
    const createRepoOptions: CreateRepositoryOptionsDto = {
      name: 'testrepo',
      description: 'Test repository',
      private: false,
    };

    it('should create repository successfully', async () => {
      const mockRepo = { id: 1, name: 'testrepo', owner: { username: 'testuser' } };
      mockAxiosInstance.post.mockResolvedValueOnce({ data: mockRepo });

      const result = await service.createRepository('testuser', createRepoOptions);

      expect(result).toEqual(mockRepo);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/admin/users/testuser/repos', {
        name: 'testrepo',
        description: 'Test repository',
        private: false,
        auto_init: true,
        gitignores: '',
        license: '',
        readme: 'Default',
        default_branch: 'main',
        trust_model: 'default',
      });
    });

    it('should handle repository creation errors', async () => {
      const error = { response: { data: { message: 'Repository already exists' } } };
      mockAxiosInstance.post.mockRejectedValueOnce(error);

      await expect(service.createRepository('testuser', createRepoOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteRepository', () => {
    it('should delete repository successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValueOnce({});

      await service.deleteRepository('testuser', 'testrepo');

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/repos/testuser/testrepo');
    });

    it('should handle delete errors', async () => {
      const error = { response: { data: { message: 'Delete failed' } } };
      mockAxiosInstance.delete.mockRejectedValueOnce(error);

      await expect(service.deleteRepository('testuser', 'testrepo')).rejects.toThrow(BadRequestException);
    });
  });

  describe('createWebhook', () => {
    const webhookOptions: WebhookOptionsDto = {
      type: 'gitea',
      config: { url: 'http://localhost:3000/api/webhooks/gitea', content_type: 'json' },
      events: ['push', 'pull_request'],
    };

    it('should create webhook successfully', async () => {
      const mockWebhook = { id: 1, type: 'gitea', active: true };
      mockAxiosInstance.post.mockResolvedValueOnce({ data: mockWebhook });

      const result = await service.createWebhook('testuser', 'testrepo', webhookOptions);

      expect(result).toEqual(mockWebhook);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks', {
        type: 'gitea',
        config: { url: 'http://localhost:3000/api/webhooks/gitea', content_type: 'json' },
        events: ['push', 'pull_request'],
        active: true,
        branch_filter: '',
      });
    });

    it('should handle webhook creation errors', async () => {
      const error = { response: { data: { message: 'Webhook creation failed' } } };
      mockAxiosInstance.post.mockRejectedValueOnce(error);

      await expect(service.createWebhook('testuser', 'testrepo', webhookOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getWebhooks', () => {
    it('should return repository webhooks', async () => {
      const mockWebhooks = [{ id: 1, type: 'gitea' }, { id: 2, type: 'slack' }];
      mockAxiosInstance.get.mockResolvedValueOnce({ data: mockWebhooks });

      const result = await service.getWebhooks('testuser', 'testrepo');

      expect(result).toEqual(mockWebhooks);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks');
    });

    it('should handle webhook fetch errors', async () => {
      const error = { response: { data: { message: 'Fetch failed' } } };
      mockAxiosInstance.get.mockRejectedValueOnce(error);

      await expect(service.getWebhooks('testuser', 'testrepo')).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteWebhook', () => {
    it('should delete webhook successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValueOnce({});

      await service.deleteWebhook('testuser', 'testrepo', 1);

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/repos/testuser/testrepo/hooks/1');
    });

    it('should handle webhook delete errors', async () => {
      const error = { response: { data: { message: 'Delete failed' } } };
      mockAxiosInstance.delete.mockRejectedValueOnce(error);

      await expect(service.deleteWebhook('testuser', 'testrepo', 1)).rejects.toThrow(BadRequestException);
    });
  });

  describe('utility methods', () => {
    it('should return correct external repository URL', () => {
      const url = service.getRepositoryExternalUrl('testuser', 'testrepo');
      expect(url).toBe('http://localhost:3001/testuser/testrepo');
    });

    it('should return correct HTTPS clone URL', () => {
      const url = service.getRepositoryCloneUrl('testuser', 'testrepo');
      expect(url).toBe('http://localhost:3001/testuser/testrepo.git');
    });

    it('should return correct SSH clone URL', () => {
      const url = service.getRepositoryCloneUrl('testuser', 'testrepo', true);
      expect(url).toBe('git@localhost:3001:testuser/testrepo.git');
    });
  });
});
