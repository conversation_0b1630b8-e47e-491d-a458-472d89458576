import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { FileUploadsService, UploadFileOptions, PresignedUploadOptions } from './file-uploads.service';
import { S3Service } from './s3.service';

// Mock database
const mockDb = {
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  returning: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
};

// Mock S3Service
const mockS3Service = {
  uploadFile: jest.fn(),
  getPresignedUploadUrl: jest.fn(),
  getPresignedDownloadUrl: jest.fn(),
  deleteFile: jest.fn(),
  fileExists: jest.fn(),
  getFileMetadata: jest.fn(),
};

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-1234'),
}));

describe('FileUploadsService', () => {
  let service: FileUploadsService;
  let s3Service: jest.Mocked<S3Service>;

  const mockUser1Id = 'user-1-id';
  const mockUser2Id = 'user-2-id';
  const mockFileId = 'file-1-id';

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test.png',
    encoding: '7bit',
    mimetype: 'image/png',
    size: 1024,
    buffer: Buffer.from('fake-image-data'),
    destination: '',
    filename: '',
    path: '',
    stream: null as any,
  };

  const mockFileUpload = {
    id: mockFileId,
    userId: mockUser1Id,
    originalName: 'test.png',
    fileName: 'mock-uuid-1234.png',
    mimeType: 'image/png',
    fileType: 'image',
    fileSize: '1024',
    s3Key: `uploads/${mockUser1Id}/2024/05/30/mock-uuid-1234.png`,
    s3Bucket: 'test-bucket',
    s3ETag: '"test-etag"',
    status: 'completed',
    uploadProgress: '100',
    isValidated: true,
    isPublic: false,
    accessToken: 'mock-access-token',
    metadata: {},
    expiresAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileUploadsService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
      ],
    }).compile();

    service = module.get<FileUploadsService>(FileUploadsService);
    s3Service = module.get(S3Service);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadFile', () => {
    const uploadOptions: UploadFileOptions = {
      userId: mockUser1Id,
      file: mockFile,
      isPublic: false,
    };

    it('should upload file successfully', async () => {
      const mockUploadResult = { key: 'test-key', etag: '"test-etag"', location: 'test-location' };
      mockS3Service.uploadFile.mockResolvedValueOnce(mockUploadResult);
      mockDb.returning.mockResolvedValueOnce([mockFileUpload]);

      const result = await service.uploadFile(uploadOptions);

      expect(result).toEqual(mockFileUpload);
      expect(mockS3Service.uploadFile).toHaveBeenCalledWith({
        key: expect.stringContaining('uploads/user-1-id/'),
        body: mockFile.buffer,
        contentType: 'image/png',
        metadata: {
          originalName: 'test.png',
          userId: mockUser1Id,
        },
        expires: undefined,
      });
    });

    it('should reject invalid file types', async () => {
      const invalidFile = { ...mockFile, mimetype: 'application/exe' };
      const invalidOptions = { ...uploadOptions, file: invalidFile };

      await expect(service.uploadFile(invalidOptions)).rejects.toThrow(BadRequestException);
    });

    it('should reject files that are too large', async () => {
      const largeFile = { ...mockFile, size: 200 * 1024 * 1024 }; // 200MB
      const largeOptions = { ...uploadOptions, file: largeFile };

      await expect(service.uploadFile(largeOptions)).rejects.toThrow(BadRequestException);
    });
  });

  describe('generatePresignedUploadUrl', () => {
    const presignedOptions: PresignedUploadOptions = {
      userId: mockUser1Id,
      fileName: 'test.png',
      fileSize: 1024,
      mimeType: 'image/png',
    };

    it('should generate presigned upload URL successfully', async () => {
      const mockPresignedUrl = 'https://s3.amazonaws.com/presigned-url';
      mockS3Service.getPresignedUploadUrl.mockResolvedValueOnce(mockPresignedUrl);
      mockDb.returning.mockResolvedValueOnce([{ ...mockFileUpload, status: 'pending' }]);

      const result = await service.generatePresignedUploadUrl(presignedOptions);

      expect(result).toEqual({
        uploadUrl: mockPresignedUrl,
        fileId: mockFileId,
        s3Key: expect.stringContaining('uploads/user-1-id/'),
      });
    });
  });

  describe('getDownloadUrl - Security Tests', () => {
    it('should allow user to access their own private file', async () => {
      mockDb.limit.mockResolvedValueOnce([mockFileUpload]);
      const mockDownloadUrl = 'https://s3.amazonaws.com/download-url';
      mockS3Service.getPresignedDownloadUrl.mockResolvedValueOnce(mockDownloadUrl);

      const result = await service.getDownloadUrl(mockFileId, mockUser1Id);

      expect(result).toBe(mockDownloadUrl);
      expect(mockS3Service.getPresignedDownloadUrl).toHaveBeenCalledWith({
        key: mockFileUpload.s3Key,
        bucket: mockFileUpload.s3Bucket,
        expires: 3600,
      });
    });

    it('should deny access to other users private files', async () => {
      mockDb.limit.mockResolvedValueOnce([mockFileUpload]);

      await expect(service.getDownloadUrl(mockFileId, mockUser2Id)).rejects.toThrow(
        new BadRequestException('Access denied')
      );
    });

    it('should allow access to public files by any user', async () => {
      const publicFile = { ...mockFileUpload, isPublic: true };
      mockDb.limit.mockResolvedValueOnce([publicFile]);
      const mockDownloadUrl = 'https://s3.amazonaws.com/download-url';
      mockS3Service.getPresignedDownloadUrl.mockResolvedValueOnce(mockDownloadUrl);

      const result = await service.getDownloadUrl(mockFileId, mockUser2Id);

      expect(result).toBe(mockDownloadUrl);
    });

    it('should allow access to public files without authentication', async () => {
      const publicFile = { ...mockFileUpload, isPublic: true };
      mockDb.limit.mockResolvedValueOnce([publicFile]);
      const mockDownloadUrl = 'https://s3.amazonaws.com/download-url';
      mockS3Service.getPresignedDownloadUrl.mockResolvedValueOnce(mockDownloadUrl);

      const result = await service.getDownloadUrl(mockFileId);

      expect(result).toBe(mockDownloadUrl);
    });

    it('should deny access to expired files', async () => {
      const expiredFile = { ...mockFileUpload, expiresAt: new Date(Date.now() - 1000) };
      mockDb.limit.mockResolvedValueOnce([expiredFile]);

      await expect(service.getDownloadUrl(mockFileId, mockUser1Id)).rejects.toThrow(
        new BadRequestException('File has expired')
      );
    });

    it('should throw NotFoundException for non-existent files', async () => {
      mockDb.limit.mockResolvedValueOnce([]);

      await expect(service.getDownloadUrl('non-existent-id', mockUser1Id)).rejects.toThrow(
        new NotFoundException('File not found')
      );
    });
  });

  describe('deleteFile - Security Tests', () => {
    it('should allow user to delete their own file', async () => {
      mockDb.limit.mockResolvedValueOnce([mockFileUpload]);
      mockS3Service.deleteFile.mockResolvedValueOnce(undefined);
      mockDb.set.mockReturnThis();

      await service.deleteFile(mockFileId, mockUser1Id);

      expect(mockS3Service.deleteFile).toHaveBeenCalledWith(
        mockFileUpload.s3Key,
        mockFileUpload.s3Bucket
      );
    });

    it('should deny user from deleting other users files', async () => {
      mockDb.limit.mockResolvedValueOnce([]);

      await expect(service.deleteFile(mockFileId, mockUser2Id)).rejects.toThrow(
        new NotFoundException('File not found')
      );
    });
  });

  describe('getUserFiles', () => {
    it('should return only the users own files', async () => {
      const userFiles = [mockFileUpload];
      mockDb.where.mockResolvedValueOnce(userFiles);

      const result = await service.getUserFiles(mockUser1Id);

      expect(result).toEqual(userFiles);
    });
  });

  describe('completeUpload', () => {
    it('should complete upload for users own file', async () => {
      const pendingFile = { ...mockFileUpload, status: 'pending' };
      mockDb.limit.mockResolvedValueOnce([pendingFile]);
      mockS3Service.fileExists.mockResolvedValueOnce(true);
      mockS3Service.getFileMetadata.mockResolvedValueOnce({
        contentLength: 1024,
        contentType: 'image/png',
      });
      mockDb.set.mockReturnThis();
      mockDb.returning.mockResolvedValueOnce([{ ...pendingFile, status: 'completed' }]);

      const result = await service.completeUpload(mockFileId, mockUser1Id);

      expect(result.status).toBe('completed');
    });

    it('should deny completing upload for other users files', async () => {
      mockDb.limit.mockResolvedValueOnce([]);

      await expect(service.completeUpload(mockFileId, mockUser2Id)).rejects.toThrow(
        new NotFoundException('File not found')
      );
    });
  });
});
