import { Modu<PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema/index.js';

@Module({
  providers: [
    {
      provide: 'DB',
      useFactory: (configService: ConfigService) => {
        const connectionString = `postgresql://${configService.get('DATABASE_USER', 'rsglider')}:${configService.get('DATABASE_PASSWORD', 'rsglider_dev_password')}@${configService.get('DATABASE_HOST', 'postgres')}:${configService.get('DATABASE_PORT', '5432')}/${configService.get('DATABASE_NAME', 'rsglider')}`;

        const client = postgres(connectionString, {
          max: 10,
          idle_timeout: 20,
          connect_timeout: 10,
        });

        // Test the connection
        client`SELECT 1 as test`.then(
          () => console.log('✅ Database connected successfully'),
          (error) => console.error('❌ Database connection failed:', error)
        );

        return drizzle(client, { schema });
      },
      inject: [ConfigService],
    },
  ],
  exports: ['DB'],
})
export class DatabaseModule {}
