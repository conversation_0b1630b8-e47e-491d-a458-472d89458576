// Export all schemas
export * from './client-releases.schema.js';
export * from './devices.schema.js';
export * from './file-uploads.schema.js';
export * from './gitea-profiles.schema.js';
export * from './gitea-repositories.schema.js';
export * from './marketplace-items.schema.js';
export * from './permissions.schema.js';
export * from './refresh-tokens.schema.js';
export * from './role-permissions.schema.js';
export * from './roles.schema.js';
export * from './user-roles.schema.js';
export * from './user-sessions.schema.js';
export * from './users.schema.js';

// Export relations
import { devicesRelations } from './devices.schema.js';
import { fileUploadsRelations } from './file-uploads.schema.js';
import { giteaProfilesRelations } from './gitea-profiles.schema.js';
import { giteaRepositoriesRelations } from './gitea-repositories.schema.js';
import { marketplaceItemsRelations } from './marketplace-items.schema.js';
import { refreshTokensRelations } from './refresh-tokens.schema.js';
import { userSessionsRelations } from './user-sessions.schema.js';

export const relations = {
  devicesRelations,
  fileUploadsRelations,
  refreshTokensRelations,
  userSessionsRelations,
  giteaProfilesRelations,
  giteaRepositoriesRelations,
  marketplaceItemsRelations,
};
