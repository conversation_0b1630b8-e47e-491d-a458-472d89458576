import { ApiProperty } from '@nestjs/swagger';

export class WebhookProcessingResultDto {
  @ApiProperty({ 
    description: 'Whether the webhook was successfully processed',
    example: true 
  })
  processed: boolean;

  @ApiProperty({ 
    description: 'The webhook action that was processed',
    example: 'created' 
  })
  action: string;

  @ApiProperty({ 
    description: 'Repository name if applicable',
    example: 'owner/repository-name',
    required: false 
  })
  repository?: string;

  @ApiProperty({ 
    description: 'Processing result message',
    example: 'Repository created and synced successfully' 
  })
  message: string;
}

export class WebhookTestResponseDto {
  @ApiProperty({ 
    description: 'Test webhook response message',
    example: 'Webhook endpoint is working correctly' 
  })
  message: string;

  @ApiProperty({ 
    description: 'Timestamp of the test',
    example: '2024-01-01T00:00:00.000Z' 
  })
  timestamp: string;

  @ApiProperty({ 
    description: 'Request headers received',
    example: { 'x-gitea-event': 'push', 'content-type': 'application/json' } 
  })
  headers: Record<string, string>;
}
