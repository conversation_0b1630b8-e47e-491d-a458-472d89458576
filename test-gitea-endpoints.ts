#!/usr/bin/env node

/**
 * Test Gitea Integration Endpoints
 * Tests the developer management functionality once the API is working
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'Test Developer'
};

let userToken: string | null = null;
let userId: string | null = null;

async function registerAndLoginUser() {
  console.log('👤 Setting up test user...');

  // Register user (might already exist)
  try {
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser, {
      validateStatus: () => true
    });

    if (registerResponse.status === 201) {
      console.log('✅ User registered successfully');
    } else if (registerResponse.status === 409) {
      console.log('ℹ️  User already exists, proceeding with login');
    }
  } catch (error) {
    console.log('ℹ️  Registration failed, user might already exist');
  }

  // Login user
  const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
    email: testUser.email,
    password: testUser.password
  });

  if (loginResponse.status === 200 && loginResponse.data.accessToken) {
    userToken = loginResponse.data.accessToken;
    userId = loginResponse.data.user.id;
    console.log(`✅ User logged in successfully (ID: ${userId})`);
    return true;
  } else {
    throw new Error('Failed to login user');
  }
}

async function testDeveloperEndpoints() {
  console.log('\n🧪 Testing Developer Management Endpoints\n');

  const headers = { 'Authorization': `Bearer ${userToken}` };

  try {
    // Test 1: Check if developer profile exists
    console.log('1️⃣ Testing GET /developers/profile...');
    const profileResponse = await axios.get(`${API_BASE}/developers/profile`, {
      headers,
      validateStatus: () => true
    });

    if (profileResponse.status === 404) {
      console.log('✅ No existing developer profile (expected for new user)');
    } else if (profileResponse.status === 200) {
      console.log('✅ Developer profile exists');
      console.log(`   Username: ${profileResponse.data.giteaUsername}`);
      console.log(`   Provisioned: ${profileResponse.data.isProvisioned}`);
      return; // Skip creation if profile already exists
    } else if (profileResponse.status === 501) {
      console.log('⚠️  Endpoint not implemented yet (expected during development)');
      return;
    } else {
      console.log(`⚠️  Unexpected response: ${profileResponse.status}`);
      console.log(`   Message: ${profileResponse.data?.message || 'No message'}`);
    }

    // Test 2: Create developer profile
    console.log('\n2️⃣ Testing POST /developers/create...');
    const createResponse = await axios.post(`${API_BASE}/developers/create`, {
      autoProvision: true
    }, {
      headers,
      validateStatus: () => true
    });

    if (createResponse.status === 201) {
      console.log('✅ Developer profile created successfully');
      console.log(`   Username: ${createResponse.data.giteaUsername}`);
      console.log(`   Provisioned: ${createResponse.data.isProvisioned}`);
    } else if (createResponse.status === 501) {
      console.log('⚠️  Endpoint not implemented yet (expected during development)');
    } else if (createResponse.status === 409) {
      console.log('ℹ️  Developer profile already exists');
    } else {
      console.log(`❌ Failed to create developer profile: ${createResponse.status}`);
      console.log(`   Message: ${createResponse.data?.message || 'No message'}`);
    }

    // Test 3: Sync repositories
    console.log('\n3️⃣ Testing POST /developers/repositories/sync...');
    const syncResponse = await axios.post(`${API_BASE}/developers/repositories/sync`, {}, {
      headers,
      validateStatus: () => true
    });

    if (syncResponse.status === 200) {
      console.log('✅ Repositories synced successfully');
      console.log(`   Synced: ${syncResponse.data.synced}`);
      console.log(`   Created: ${syncResponse.data.created}`);
      console.log(`   Updated: ${syncResponse.data.updated}`);
      console.log(`   Errors: ${syncResponse.data.errors?.length || 0}`);
    } else if (syncResponse.status === 501) {
      console.log('⚠️  Endpoint not implemented yet (expected during development)');
    } else {
      console.log(`❌ Failed to sync repositories: ${syncResponse.status}`);
      console.log(`   Message: ${syncResponse.data?.message || 'No message'}`);
    }

    // Test 4: Get repositories
    console.log('\n4️⃣ Testing GET /developers/repositories...');
    const reposResponse = await axios.get(`${API_BASE}/developers/repositories`, {
      headers,
      validateStatus: () => true
    });

    if (reposResponse.status === 200) {
      console.log('✅ Repositories retrieved successfully');
      console.log(`   Count: ${reposResponse.data.length}`);
      if (reposResponse.data.length > 0) {
        console.log(`   First repo: ${reposResponse.data[0].fullName}`);
      }
    } else if (reposResponse.status === 501) {
      console.log('⚠️  Endpoint not implemented yet (expected during development)');
    } else {
      console.log(`❌ Failed to get repositories: ${reposResponse.status}`);
      console.log(`   Message: ${reposResponse.data?.message || 'No message'}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

async function testWebhookEndpoint() {
  console.log('\n🪝 Testing Webhook Endpoint\n');

  try {
    // Test webhook endpoint
    console.log('1️⃣ Testing POST /webhooks/gitea/test...');
    const webhookResponse = await axios.post(`${API_BASE}/webhooks/gitea/test`, {
      test: 'data',
      timestamp: new Date().toISOString()
    }, {
      validateStatus: () => true
    });

    if (webhookResponse.status === 200) {
      console.log('✅ Webhook endpoint working');
      console.log(`   Message: ${webhookResponse.data.message}`);
    } else if (webhookResponse.status === 501) {
      console.log('⚠️  Webhook endpoint not implemented yet (expected during development)');
    } else {
      console.log(`❌ Webhook test failed: ${webhookResponse.status}`);
      console.log(`   Message: ${webhookResponse.data?.message || 'No message'}`);
    }

  } catch (error) {
    console.error('❌ Webhook test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

async function runEndpointTests() {
  console.log('🧪 Starting Gitea Integration Endpoint Tests\n');

  try {
    // Setup
    const userSetup = await registerAndLoginUser();

    if (!userSetup) {
      console.log('\n❌ Cannot proceed without user authentication');
      return;
    }

    // Test developer endpoints
    await testDeveloperEndpoints();

    // Test webhook endpoints
    await testWebhookEndpoint();

    console.log('\n📊 Endpoint Test Summary:');
    console.log('========================');
    console.log('✅ User authentication working');
    console.log('⚠️  Most endpoints expected to be 501 (not implemented) during development');
    console.log('🎯 Goal: Verify API structure and routing is working');

  } catch (error) {
    console.error('❌ Test setup failed:', error.message);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runEndpointTests().catch(console.error);
}

export { runEndpointTests };
