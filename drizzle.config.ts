import * as dotenv from 'dotenv';
import { defineConfig } from 'drizzle-kit';

// Load environment variables (.env.local first for overrides, then .env for defaults)
dotenv.config({ path: '.env.local', override: true });
dotenv.config();

export default defineConfig({
  schema: './src/database/schema/*',
  out: './src/database/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    user: process.env.DATABASE_USER || 'rsglider',
    password: process.env.DATABASE_PASSWORD || 'rsglider_dev_password',
    database: process.env.DATABASE_NAME || 'rsglider',
    ssl: false,
  },
  verbose: true,
  strict: true,
});
