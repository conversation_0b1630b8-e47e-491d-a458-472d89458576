import { expect, test } from '@playwright/test';
import { RegisterRequest } from '../src/auth/dto/register-request.dto.js';
import { CreateRepositoryOptionsDto } from '../src/common/dto/create-repository-options.dto.js';
import { CreateUserOptionsDto } from '../src/common/dto/create-user-options.dto.js';

test.describe('Gitea Integration API', () => {
  let authToken: string;
  let userId: string;

  test.beforeAll(async ({ request }) => {
    // Register and login to get auth token
    const userData: RegisterRequest = {
      email: `gitea_test_${Date.now()}@example.com`,
      password: 'SecurePass123!',
      name: 'Gitea Test',
    };

    const registerResponse = await request.post('/api/auth/register', {
      data: userData,
    });
    expect(registerResponse.ok()).toBeTruthy();

    const registerBody = await registerResponse.json();
    authToken = registerBody.access_token;
    userId = registerBody.user.id;
  });

  test('should check Gitea health', async ({ request }) => {
    const response = await request.get('/api/gitea/health', {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const body = await response.json();
    expect(body).toHaveProperty('status');
    expect(body).toHaveProperty('version');
  });

  test('should create a Gitea user', async ({ request }) => {
    const giteaUserData: CreateUserOptionsDto = {
      username: `testuser_${Date.now()}`,
      email: `gitea_user_${Date.now()}@example.com`,
      password: 'SecurePass123!',
      full_name: 'Test Gitea User',
      send_notify: false,
      must_change_password: false,
      restricted: false,
      visibility: 'public',
    };

    const response = await request.post('/api/gitea/users', {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      data: giteaUserData,
    });

    if (response.status() === 201) {
      const body = await response.json();
      expect(body).toHaveProperty('id');
      expect(body.login).toBe(giteaUserData.username);
      expect(body.email).toBe(giteaUserData.email);
    } else if (response.status() === 422) {
      // User might already exist, that's okay for testing
      const body = await response.json();
      console.log('User creation response:', body);
    } else {
      throw new Error(`Unexpected status: ${response.status()}`);
    }
  });

  test('should create a repository', async ({ request }) => {
    const repoData: CreateRepositoryOptionsDto = {
      name: `test-repo-${Date.now()}`,
      description: 'Test repository created by API tests',
      private: false,
      auto_init: true,
      gitignores: '',
      license: '',
      readme: 'Default',
      default_branch: 'main',
      trust_model: 'default',
    };

    const response = await request.post('/api/gitea/repositories', {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      data: repoData,
    });

    if (response.status() === 201) {
      const body = await response.json();
      expect(body).toHaveProperty('id');
      expect(body.name).toBe(repoData.name);
      expect(body.description).toBe(repoData.description);
      expect(body.private).toBe(repoData.private);
    } else {
      // Log the error for debugging
      const body = await response.json();
      console.log('Repository creation response:', response.status(), body);
    }
  });

  test('should list user repositories', async ({ request }) => {
    const response = await request.get('/api/gitea/repositories', {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    
    const body = await response.json();
    expect(Array.isArray(body)).toBeTruthy();
    // Should have at least the repository we created in previous test
    expect(body.length).toBeGreaterThanOrEqual(0);
  });

  test('should handle Gitea errors gracefully', async ({ request }) => {
    // Try to create a user with invalid data
    const invalidUserData = {
      username: '', // Invalid: empty username
      email: 'invalid-email', // Invalid: bad email format
      password: '123', // Invalid: weak password
    };

    const response = await request.post('/api/gitea/users', {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      data: invalidUserData,
    });

    expect(response.status()).toBeGreaterThanOrEqual(400);
    expect(response.status()).toBeLessThan(500);
  });
}); 