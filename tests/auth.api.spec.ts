import { expect, test } from '@playwright/test';
import { LoginRequest } from '../src/auth/dto/login-request.dto.js';
import { RegisterRequest } from '../src/auth/dto/register-request.dto.js';

test.describe('Authentication API', () => {
  test('should return health status', async ({ request }) => {
    const response = await request.get('/health');
    expect(response.ok()).toBeTruthy();
    
    const body = await response.json();
    expect(body).toHaveProperty('status', 'ok');
    expect(body).toHaveProperty('timestamp');
    expect(body).toHaveProperty('uptime');
  });

  test('should register a new user', async ({ request }) => {
    const userData: RegisterRequest = {
      email: `test_${Date.now()}@example.com`,
      password: 'SecurePass123!',
      name: 'Test User',
    };

    const response = await request.post('/api/auth/register', {
      data: userData,
    });

    expect(response.status()).toBe(201);
    
    const body = await response.json();
    expect(body).toHaveProperty('user');
    expect(body).toHaveProperty('access_token');
    expect(body.user.email).toBe(userData.email);
    expect(body.user.name).toBe(userData.name);
  });

  test('should reject weak password', async ({ request }) => {
    const userData: RegisterRequest = {
      email: `weak_${Date.now()}@example.com`,
      password: '123',
      name: 'Weak Password',
    };

    const response = await request.post('/api/auth/register', {
      data: userData,
    });

    expect(response.status()).toBe(400);
    
    const body = await response.json();
    expect(body.message).toContain('password');
  });

  test('should login with valid credentials', async ({ request }) => {
    // First register a user
    const userData: RegisterRequest = {
      email: `login_${Date.now()}@example.com`,
      password: 'SecurePass123!',
      name: 'Login Test',
    };

    const registerResponse = await request.post('/api/auth/register', {
      data: userData,
    });
    expect(registerResponse.ok()).toBeTruthy();

    // Then login
    const loginData: LoginRequest = {
      email: userData.email,
      password: userData.password,
    };

    const loginResponse = await request.post('/api/auth/login', {
      data: loginData,
    });

    expect(loginResponse.status()).toBe(200);
    
    const body = await loginResponse.json();
    expect(body).toHaveProperty('access_token');
    expect(body).toHaveProperty('user');
    expect(body.user.email).toBe(userData.email);
  });

  test('should reject invalid login credentials', async ({ request }) => {
    const loginData: LoginRequest = {
      email: '<EMAIL>',
      password: 'WrongPassword123!',
    };

    const response = await request.post('/api/auth/login', {
      data: loginData,
    });

    expect(response.status()).toBe(401);
    
    const body = await response.json();
    expect(body.message).toContain('Invalid');
  });

  test('should access protected route with valid token', async ({ request }) => {
    // Register and login to get token
    const userData: RegisterRequest = {
      email: `protected_${Date.now()}@example.com`,
      password: 'SecurePass123!',
      name: 'Protected Test',
    };

    const registerResponse = await request.post('/api/auth/register', {
      data: userData,
    });
    const registerBody = await registerResponse.json();
    const token = registerBody.access_token;

    // Access protected route
    const profileResponse = await request.get('/api/auth/profile', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    expect(profileResponse.status()).toBe(200);
    
    const body = await profileResponse.json();
    expect(body.email).toBe(userData.email);
  });

  test('should reject protected route without token', async ({ request }) => {
    const response = await request.get('/api/auth/profile');
    expect(response.status()).toBe(401);
  });

  test('should reject protected route with invalid token', async ({ request }) => {
    const response = await request.get('/api/auth/profile', {
      headers: {
        Authorization: 'Bearer invalid_token_here',
      },
    });
    expect(response.status()).toBe(401);
  });
}); 