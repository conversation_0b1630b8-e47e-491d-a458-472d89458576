#!/bin/bash

set -e

echo "🚀 RSGlider API Startup Script"
echo "==============================================="

# Configuration
MAX_WAIT_TIME=120
POSTGRES_HOST=${DATABASE_HOST:-postgres}
POSTGRES_PORT=${DATABASE_PORT:-5432}
POSTGRES_DB=${DATABASE_NAME:-rsglider}
POSTGRES_USER=${DATABASE_USER:-rsglider}

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=$((MAX_WAIT_TIME / 3))
    local attempt=0

    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -lt $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        echo "   $service_name not ready yet, waiting... (attempt $((attempt + 1))/$max_attempts)"
        sleep 3
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to become ready after $MAX_WAIT_TIME seconds"
    return 1
}

# Function to wait for database with connection test
wait_for_database() {
    echo "⏳ Waiting for PostgreSQL database..."
    local max_attempts=$((MAX_WAIT_TIME / 5))
    local attempt=0

    while [ $attempt -lt $max_attempts ]; do
        if nc -z "$POSTGRES_HOST" "$POSTGRES_PORT" 2>/dev/null; then
            echo "✅ PostgreSQL database is ready!"
            return 0
        fi
        echo "   Database not ready yet, waiting... (attempt $((attempt + 1))/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ Database failed to become ready after $MAX_WAIT_TIME seconds"
    return 1
}

# Function to run database migrations
run_migrations() {
    echo "🗄️  Database migrations..."
    echo "   Database tables are created by PostgreSQL init scripts"
    echo "   Skipping Drizzle Kit migrations (handled by init scripts)"
    echo "✅ Database setup completed!"
}

# Function to seed initial data
seed_data() {
    echo "🌱 Initial data setup..."
    echo "   Database tables will be created by migrations"
    echo "   Initial admin user will be created by the API on first startup"
    echo "✅ Data setup completed!"
}

# Function to wait for Gitea and get API token
setup_gitea_integration() {
    echo "🔧 Setting up Gitea integration..."
    
    # Wait for Gitea to be ready
    wait_for_service gitea 3000 "Gitea"
    
    # Wait for Gitea admin token to be created
    local max_attempts=40
    local attempt=0
    local token_file="/app/shared/gitea_admin_token.txt"
    
    echo "⏳ Waiting for Gitea admin token..."
    while [ $attempt -lt $max_attempts ]; do
        if [ -f "$token_file" ] && [ -s "$token_file" ]; then
            local token=$(cat "$token_file" 2>/dev/null || echo "")
            if [ -n "$token" ] && [ "$token" != "null" ]; then
                echo "✅ Gitea admin token available!"
                export GITEA_ADMIN_TOKEN="$token"
                echo "🔑 Token set in environment"
                return 0
            fi
        fi
        echo "   Gitea token not ready yet, waiting... (attempt $((attempt + 1))/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "⚠️  Gitea token not available yet, continuing without it"
    return 0
}

# Main startup sequence
echo "🏁 Starting RSGlider API initialization..."

# Step 1: Wait for dependencies
echo ""
echo "📊 Step 1: Waiting for dependencies..."
wait_for_service redis 6379 "Redis"
wait_for_service minio 9000 "MinIO"
wait_for_database

# Step 2: Run database migrations
echo ""
echo "📊 Step 2: Database setup..."
run_migrations

# Step 3: Seed initial data
echo ""
echo "📊 Step 3: Data seeding..."
seed_data

# Step 4: Setup external integrations
echo ""
echo "📊 Step 4: External integrations..."
setup_gitea_integration

# Step 5: Start the application
echo ""
echo "📊 Step 5: Starting application..."
echo "🎉 All initialization completed successfully!"
echo ""
echo "🌐 RSGlider API will start on http://localhost:3000"
echo "📚 API Documentation: http://localhost:3000/api/docs"
echo "🔧 Gitea Web UI: http://localhost:3001"
echo "📧 Inbucket (Email): http://localhost:9025"
echo "💾 MinIO Console: http://localhost:9002"
echo ""

# Start the NestJS application
exec pnpm run start:dev 