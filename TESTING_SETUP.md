# Testing Setup for RSGlider API

## Overview

The RSGlider API now has a comprehensive testing setup with multiple types of tests:

## Test Types

### 1. Unit Tests (Jest)
- **Location**: `src/**/*.spec.ts`
- **Command**: `pnpm run test`
- **Description**: Unit tests for individual services, controllers, and utilities
- **Configuration**: Jest with TypeScript support

### 2. End-to-End Tests (Playwright)
- **Location**: `tests/**/*.api.spec.ts`
- **Command**: `pnpm run test:e2e`
- **Description**: API endpoint tests using Playwright
- **Configuration**: Playwright with API testing setup

### 3. Integration Tests (Standalone)
- **Location**: Root directory `test-*.ts` files
- **Commands**:
  - `pnpm run test:integration` - S3/MinIO integration tests
  - `pnpm run test:gitea` - Gitea integration tests
  - `pnpm run test:security` - Security and permissions tests
  - `pnpm run test:standalone` - All standalone tests
- **Description**: Integration tests for external services

## Running Tests

### All Tests
```bash
pnpm run test:all
```

### Individual Test Types
```bash
# Unit tests only
pnpm run test

# E2E tests only
pnpm run test:e2e

# Integration tests only
pnpm run test:standalone

# With coverage
pnpm run test:cov

# Watch mode
pnpm run test:watch
```

## Test Configuration

### Jest Configuration
- **Preset**: `ts-jest`
- **Environment**: `node`
- **Setup**: `test/setup.ts` provides global test utilities
- **Module mapping**: Supports `@/` path aliases
- **Coverage**: Configured to collect from all TypeScript files

### TypeScript Configuration
- **Module resolution**: NodeNext for ES module support
- **Jest-specific config**: Uses CommonJS for Jest compilation to avoid decorator issues

## Writing Tests

### Unit Test Example
```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { MyService } from './my.service';

describe('MyService', () => {
  let service: MyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MyService],
    }).compile();

    service = module.get<MyService>(MyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
```

### Global Test Utilities
The `test/setup.ts` file provides global utilities:
- `testUtils.createMockUser()` - Creates a mock user object
- `testUtils.createMockRequest()` - Creates a mock Express request
- `testUtils.createMockResponse()` - Creates a mock Express response

## Current Test Status

### Working Tests
- ✅ Basic Jest setup and configuration
- ✅ Utility function tests
- ✅ Redis service tests
- ✅ Playwright E2E tests (auth, gitea)
- ✅ Standalone integration tests

### Needs Implementation
- ⏳ Auth service unit tests (complex due to ES modules)
- ⏳ Controller unit tests
- ⏳ Database service tests
- ⏳ More comprehensive service tests

## Troubleshooting

### Common Issues

1. **Module resolution errors**: Ensure imports use relative paths or properly configured aliases
2. **ES module issues**: The project uses ES modules, which can cause import issues in tests
3. **Database mocking**: Use proper mocking for database operations in unit tests

### Solutions Applied

1. **Jest configuration**: Added proper TypeScript and ES module support
2. **Test setup**: Created global test utilities and proper module declarations
3. **Path mapping**: Configured module name mapping for cleaner imports
4. **Isolated modules**: Enabled in TypeScript for better Jest compatibility

## Next Steps

1. **Expand unit test coverage**: Add tests for all services and controllers
2. **Database testing**: Set up proper database mocking or test database
3. **Authentication testing**: Improve auth service testing with proper mocking
4. **CI/CD integration**: Ensure all tests run in continuous integration
