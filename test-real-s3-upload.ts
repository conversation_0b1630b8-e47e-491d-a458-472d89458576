#!/usr/bin/env node

/**
 * Real S3 Upload Test
 * This script tests actual file upload functionality with authentication
 */

import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const API_BASE = 'http://localhost:3000/api';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'S3 Tester'
};

let authToken: string | null = null;

async function registerTestUser() {
  console.log('👤 Registering test user...');
  try {
    const response = await axios.post(`${API_BASE}/auth/register`, testUser, {
      validateStatus: () => true
    });

    if (response.status === 201) {
      console.log('✅ Test user registered successfully');
      return true;
    } else if (response.status === 409) {
      console.log('✅ Test user already exists');
      return true;
    } else {
      console.log('❌ Failed to register test user:', response.status, response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ Registration failed:', error.message);
    return false;
  }
}

async function loginTestUser() {
  console.log('🔐 Logging in test user...');
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });

    if (response.status === 200 && response.data.accessToken) {
      authToken = response.data.accessToken;
      console.log('✅ Test user logged in successfully');
      return true;
    } else {
      console.log('❌ Login failed:', response.status, response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    return false;
  }
}

async function createTestImageFile() {
  console.log('📄 Creating test image file...');

  // Create a simple 1x1 PNG image (smallest valid PNG)
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // Width: 1
    0x00, 0x00, 0x00, 0x01, // Height: 1
    0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth: 8, Color type: 2 (RGB), Compression: 0, Filter: 0, Interlace: 0
    0x90, 0x77, 0x53, 0xDE, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Image data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);

  const testFilePath = path.join(__dirname, 'test-upload.png');
  fs.writeFileSync(testFilePath, pngData);
  console.log('✅ Test PNG image created:', testFilePath);
  return testFilePath;
}

async function testDirectFileUpload() {
  console.log('📤 Testing direct file upload...');

  const testFilePath = await createTestImageFile();

  try {
    const form = new FormData();
    form.append('file', fs.createReadStream(testFilePath));
    form.append('isPublic', 'false');

    const response = await axios.post(`${API_BASE}/uploads/direct`, form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.status === 201) {
      console.log('✅ Direct upload successful:', response.data);

      // Clean up test file
      fs.unlinkSync(testFilePath);

      return {
        success: true,
        fileId: response.data.id,
        downloadUrl: response.data.downloadUrl
      };
    } else {
      console.log('❌ Direct upload failed:', response.status, response.data);
      return { success: false };
    }
  } catch (error) {
    console.error('❌ Direct upload failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.status, error.response.data);
    }

    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }

    return { success: false };
  }
}

async function testPresignedUpload() {
  console.log('📝 Testing presigned upload...');

  const testFilePath = await createTestImageFile();
  const fileStats = fs.statSync(testFilePath);

  try {
    // Step 1: Get presigned URL
    const presignedResponse = await axios.post(`${API_BASE}/uploads/presigned`, {
      fileName: 'test-presigned-upload.png',
      fileSize: fileStats.size,
      mimeType: 'image/png'
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (presignedResponse.status !== 201) {
      console.log('❌ Failed to get presigned URL:', presignedResponse.status);
      return { success: false };
    }

    console.log('✅ Presigned URL generated');
    const { uploadUrl, fileId } = presignedResponse.data;

    // Step 2: Upload to presigned URL
    const fileContent = fs.readFileSync(testFilePath);
    const uploadResponse = await axios.put(uploadUrl, fileContent, {
      headers: {
        'Content-Type': 'image/png'
      },
      validateStatus: () => true
    });

    if (uploadResponse.status !== 200) {
      console.log('❌ Failed to upload to presigned URL:', uploadResponse.status);
      return { success: false };
    }

    console.log('✅ File uploaded to S3 via presigned URL');

    // Step 3: Complete the upload
    const completeResponse = await axios.post(`${API_BASE}/uploads/${fileId}/complete`, {}, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (completeResponse.status === 200 || completeResponse.status === 201) {
      console.log('✅ Presigned upload completed:', completeResponse.data);

      // Clean up test file
      fs.unlinkSync(testFilePath);

      return {
        success: true,
        fileId: completeResponse.data.id,
        downloadUrl: completeResponse.data.downloadUrl
      };
    } else {
      console.log('❌ Failed to complete upload:', completeResponse.status);
      return { success: false };
    }

  } catch (error) {
    console.error('❌ Presigned upload failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.status, error.response.data);
    }

    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }

    return { success: false };
  }
}

async function testFileDownload(fileId) {
  console.log('📥 Testing file download...');

  try {
    // Get download URL
    const response = await axios.get(`${API_BASE}/uploads/${fileId}/download`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.status === 200 && response.data.downloadUrl) {
      console.log('✅ Download URL generated');

      // Test downloading the file
      const downloadResponse = await axios.get(response.data.downloadUrl);

      if (downloadResponse.status === 200) {
        console.log('✅ File downloaded successfully');
        console.log('📄 File content preview:', downloadResponse.data.substring(0, 100) + '...');
        return true;
      } else {
        console.log('❌ Failed to download file:', downloadResponse.status);
        return false;
      }
    } else {
      console.log('❌ Failed to get download URL:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Download test failed:', error.message);
    return false;
  }
}

async function testListUserFiles() {
  console.log('📋 Testing list user files...');

  try {
    const response = await axios.get(`${API_BASE}/uploads/my-files`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.status === 200) {
      console.log('✅ User files retrieved:', response.data.length, 'files');
      response.data.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.originalName} (${file.fileSize} bytes)`);
      });
      return true;
    } else {
      console.log('❌ Failed to list files:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ List files failed:', error.message);
    return false;
  }
}

async function runRealS3Tests() {
  console.log('🚀 Starting Real S3 Upload Tests\n');

  const tests = [
    { name: 'Register Test User', fn: registerTestUser },
    { name: 'Login Test User', fn: loginTestUser },
    { name: 'Direct File Upload', fn: testDirectFileUpload },
    { name: 'Presigned Upload', fn: testPresignedUpload },
    { name: 'List User Files', fn: testListUserFiles },
  ];

  const results = [];
  let uploadedFiles = [];

  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    const result = await test.fn();

    if (typeof result === 'object' && result.success !== undefined) {
      results.push({ name: test.name, passed: result.success });
      if (result.success && result.fileId) {
        uploadedFiles.push(result);
      }
    } else {
      results.push({ name: test.name, passed: result });
    }
  }

  // Test downloads for uploaded files
  for (const file of uploadedFiles) {
    console.log(`\n--- Download Test for ${file.fileId} ---`);
    const downloadResult = await testFileDownload(file.fileId);
    results.push({ name: `Download ${file.fileId}`, passed: downloadResult });
  }

  console.log('\n📊 Real S3 Test Results:');
  console.log('========================');

  let passed = 0;
  let failed = 0;

  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.passed) passed++;
    else failed++;
  });

  console.log(`\nTotal: ${passed + failed} tests, ${passed} passed, ${failed} failed`);

  if (failed === 0) {
    console.log('\n🎉 All S3 tests passed! S3 integration is 100% working!');
  } else {
    console.log('\n⚠️  Some S3 tests failed. Check the logs above.');
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runRealS3Tests().catch(console.error);
}

export { runRealS3Tests };
