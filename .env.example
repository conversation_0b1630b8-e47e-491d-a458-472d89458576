# RSGlider API Environment Configuration
# Copy this file to .env and update the values

# Application
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=rsglider
DATABASE_USER=rsglider
DATABASE_PASSWORD=rsglider_dev_password

# Redis Configuration (Sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=rsglider_redis_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# BTCPay Server Configuration
BTCPAY_SERVER_URL=https://btcpay.rsglider.com
BTCPAY_STORE_ID=your_btcpay_store_id
BTCPAY_API_KEY=your_btcpay_api_key
BTCPAY_WEBHOOK_SECRET=your_btcpay_webhook_secret

# Gitea Integration
GITEA_BASE_URL=https://git.rsglider.com
GITEA_ADMIN_TOKEN=your_gitea_admin_token
GITEA_OIDC_CLIENT_ID=your_gitea_oidc_client_id
GITEA_OIDC_CLIENT_SECRET=your_gitea_oidc_client_secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,https://app.rsglider.com
CORS_CREDENTIALS=true

# Session Configuration
SESSION_SECRET=your_session_secret_change_in_production
SESSION_MAX_AGE=86400000  # 24 hours

# Two-Factor Authentication
TOTP_ISSUER=RSGlider
TOTP_WINDOW=2

# Payout Configuration
DEFAULT_PAYOUT_FREQUENCY=weekly
MINIMUM_PAYOUT_AMOUNT=50
PAYOUT_CURRENCY=USD

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_2FA=true
ENABLE_DEVELOPER_SIGNUP=true
ENABLE_MARKETPLACE=true

# Development Only
DEBUG_SQL=false
ENABLE_SWAGGER=true
SWAGGER_PATH=/api/docs
