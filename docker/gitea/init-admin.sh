#!/bin/bash

# Gitea Admin User Auto-Creation Script
# This script automatically creates an admin user and generates an API token

set -e

echo "🔧 Gitea Admin Initialization Script"

# Configuration
ADMIN_USERNAME="admin"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="rsglider_admin_password_change_in_production"
TOKEN_NAME="rsglider_api_token"
SHARED_DIR="/shared"
TOKEN_FILE="$SHARED_DIR/gitea_admin_token.txt"

# Ensure shared directory exists
mkdir -p "$SHARED_DIR"

# Function to extract JSON value without jq
extract_json_value() {
    local json="$1"
    local key="$2"
    echo "$json" | sed -n "s/.*\"$key\":\"\\([^\"]*\\)\".*/\\1/p"
}

# Function to test if a token is valid
test_token_validity() {
    local token="$1"
    if [ -z "$token" ]; then
        return 1
    fi
    
    local test_response=$(curl -s -H "Authorization: token $token" "http://gitea:3000/api/v1/user" 2>/dev/null || echo "")
    if echo "$test_response" | grep -q '"login"'; then
        local user_login=$(extract_json_value "$test_response" "login")
        echo "✅ Token is valid for user: $user_login"
        return 0
    else
        echo "❌ Token is invalid"
        return 1
    fi
}

# Function to check if token exists and is valid
check_existing_token() {
    if [ -f "$TOKEN_FILE" ]; then
        local existing_token=$(cat "$TOKEN_FILE" 2>/dev/null || echo "")
        if [ -n "$existing_token" ] && [ "$existing_token" != "null" ]; then
            echo "🔍 Found existing token file, testing validity..."
            if test_token_validity "$existing_token"; then
                echo "🔑 Using existing valid token"
                return 0
            else
                echo "⚠️  Existing token is invalid, will generate new one"
                rm -f "$TOKEN_FILE"
            fi
        fi
    fi
    return 1
}

# Wait for Gitea to be ready
echo "⏳ Waiting for Gitea to be ready..."
max_attempts=60
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -f http://gitea:3000/api/healthz >/dev/null 2>&1; then
        echo "✅ Gitea is ready!"
        break
    fi
    echo "   Gitea not ready yet, waiting... (attempt $((attempt + 1))/$max_attempts)"
    sleep 3
    attempt=$((attempt + 1))
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ Gitea failed to become ready after $max_attempts attempts"
    exit 1
fi

# Wait for Gitea to fully initialize
echo "⏳ Waiting for Gitea to fully initialize..."
sleep 15

# Check if we already have a valid token
if check_existing_token; then
    echo "🎉 Gitea admin token already available and valid!"
    echo ""
    echo "📋 Admin Credentials:"
    echo "   Username: $ADMIN_USERNAME"
    echo "   Email:    $ADMIN_EMAIL"
    echo "   Password: $ADMIN_PASSWORD"
    echo "   Web UI:   http://localhost:3001"
    echo "   Token:    $(cat "$TOKEN_FILE" 2>/dev/null || echo "Not found")"
    echo ""
    echo "✅ Gitea is ready for RSGlider integration!"
    exit 0
fi

# Check if Gitea is already installed
echo "🔍 Checking if Gitea is already installed..."
INSTALL_CHECK=$(curl -s -o /dev/null -w "%{http_code}" "http://gitea:3000/user/login")

if [ "$INSTALL_CHECK" = "200" ]; then
    echo "✅ Gitea is already installed and configured"
    GITEA_INSTALLED=true
else
    echo "🔧 Gitea needs to be installed"
    GITEA_INSTALLED=false
fi

# If Gitea is not installed, run the installation
if [ "$GITEA_INSTALLED" = "false" ]; then
    echo "🚀 Installing Gitea with admin user..."

    INSTALL_RESPONSE=$(curl -s -X POST "http://gitea:3000/install" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "db_type=postgres" \
        -d "db_host=postgres:5432" \
        -d "db_user=rsglider" \
        -d "db_passwd=rsglider_dev_password" \
        -d "db_name=rsglider_gitea" \
        -d "ssl_mode=disable" \
        -d "db_schema=" \
        -d "charset=utf8" \
        -d "db_path=" \
        -d "app_name=RSGlider" \
        -d "repo_root_path=/data/git/repositories" \
        -d "lfs_root_path=/data/git/lfs" \
        -d "run_user=git" \
        -d "domain=localhost" \
        -d "ssh_port=22" \
        -d "http_port=3000" \
        -d "app_url=http://localhost:3001/" \
        -d "log_root_path=/data/gitea/log" \
        -d "smtp_host=" \
        -d "smtp_from=" \
        -d "smtp_user=" \
        -d "smtp_passwd=" \
        -d "enable_federated_avatar=on" \
        -d "enable_open_id_sign_in=on" \
        -d "enable_open_id_sign_up=on" \
        -d "default_allow_create_organization=on" \
        -d "default_enable_timetracking=on" \
        -d "no_reply_address=noreply.localhost" \
        -d "password_algorithm=pbkdf2" \
        -d "admin_name=$ADMIN_USERNAME" \
        -d "admin_passwd=$ADMIN_PASSWORD" \
        -d "admin_confirm_passwd=$ADMIN_PASSWORD" \
        -d "admin_email=$ADMIN_EMAIL")

    echo "📋 Install response received"

    # Wait for installation to complete
    echo "⏳ Waiting for installation to complete..."
    sleep 10

    # Verify installation worked
    LOGIN_CHECK=$(curl -s -o /dev/null -w "%{http_code}" "http://gitea:3000/user/login")
    if [ "$LOGIN_CHECK" = "200" ]; then
        echo "✅ Gitea installation completed successfully"
    else
        echo "❌ Gitea installation may have failed (HTTP $LOGIN_CHECK)"
        echo "🔍 Install response: $INSTALL_RESPONSE"
        exit 1
    fi
else
    # If Gitea is already installed, ensure admin password is correct and doesn't require change
    echo "🔐 Ensuring admin password is set correctly..."
    gitea admin user change-password --username "$ADMIN_USERNAME" --password "$ADMIN_PASSWORD" --must-change-password=false 2>/dev/null || true
fi

# Now generate an API token
echo "🔑 Generating API token..."

# Check for existing tokens with the same name and clean them up
echo "🔍 Checking for existing tokens with name '$TOKEN_NAME'..."
EXISTING_TOKENS_RESPONSE=$(curl -s -u "$ADMIN_USERNAME:$ADMIN_PASSWORD" "http://gitea:3000/api/v1/users/$ADMIN_USERNAME/tokens" 2>/dev/null || echo "")

if echo "$EXISTING_TOKENS_RESPONSE" | grep -q "\"$TOKEN_NAME\""; then
    echo "⚠️  Token with name '$TOKEN_NAME' already exists, will use timestamped name"
    TOKEN_NAME="${TOKEN_NAME}_$(date +%s)"
    echo "ℹ️  Using token name: $TOKEN_NAME"
fi

# Generate new token using basic auth as per Gitea API docs
echo "🔄 Generating new API token via Gitea API..."
TOKEN_RESPONSE=$(curl -s -X POST "http://gitea:3000/api/v1/users/$ADMIN_USERNAME/tokens" \
    -H "Content-Type: application/json" \
    -u "$ADMIN_USERNAME:$ADMIN_PASSWORD" \
    -d "{\"name\":\"$TOKEN_NAME\",\"scopes\":[\"all\"]}")

echo "📋 Token API Response: $TOKEN_RESPONSE"

# Parse the response and extract the token
if echo "$TOKEN_RESPONSE" | grep -q '"sha1"'; then
    # Extract the token using jq if available, otherwise use sed
    if command -v jq >/dev/null 2>&1; then
        TOKEN_RESULT=$(echo "$TOKEN_RESPONSE" | jq -r '.sha1' 2>/dev/null)
    else
        # Fallback without jq - extract sha1 value
        TOKEN_RESULT=$(extract_json_value "$TOKEN_RESPONSE" "sha1")
    fi

    if [ -n "$TOKEN_RESULT" ] && [ "$TOKEN_RESULT" != "null" ] && [ "$TOKEN_RESULT" != "" ]; then
        echo "✅ API token generated successfully!"
        echo "🔑 Token: $TOKEN_RESULT"
        
        # Save token to shared file
        echo "$TOKEN_RESULT" > "$TOKEN_FILE"
        echo "💾 Token saved to $TOKEN_FILE"

        # Verify the token works
        echo "🧪 Testing token authentication..."
        if test_token_validity "$TOKEN_RESULT"; then
            echo "✅ Token verification successful!"
        else
            echo "⚠️  Token verification failed, but token was generated"
        fi
    else
        echo "❌ Failed to extract valid token from response"
        echo "🔍 Raw token extraction result: '$TOKEN_RESULT'"
        exit 1
    fi
else
    echo "❌ Token generation failed"
    echo "🔍 Full response: $TOKEN_RESPONSE"
    
    # Check for common error patterns
    if echo "$TOKEN_RESPONSE" | grep -q "unauthorized"; then
        echo "❌ Authentication failed - check admin username and password"
        echo "ℹ️  Current admin username: $ADMIN_USERNAME"
        echo "ℹ️  Verify the admin user was created during installation"
    elif echo "$TOKEN_RESPONSE" | grep -q "already exists"; then
        echo "❌ Token name already exists - this shouldn't happen with timestamp"
    elif echo "$TOKEN_RESPONSE" | grep -q "forbidden"; then
        echo "❌ Forbidden - admin user may not have permission to create tokens"
    elif echo "$TOKEN_RESPONSE" | grep -q "not found"; then
        echo "❌ User not found - admin user may not exist"
    else
        echo "❌ Unknown error occurred during token generation"
    fi
    
    exit 1
fi

echo ""
echo "🎉 Gitea admin initialization completed!"
echo ""
echo "📋 Admin Credentials:"
echo "   Username: $ADMIN_USERNAME"
echo "   Email:    $ADMIN_EMAIL"
echo "   Password: $ADMIN_PASSWORD"
echo "   Web UI:   http://localhost:3001"
echo "   Token:    $(cat "$TOKEN_FILE" 2>/dev/null || echo "Not found")"
echo ""
echo "✅ Gitea is ready for RSGlider integration!"
